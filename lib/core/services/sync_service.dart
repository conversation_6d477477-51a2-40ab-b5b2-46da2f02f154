import 'dart:async';
import 'dart:developer';
import 'dart:isolate';
import 'dart:math' as math;
import 'package:hive_ce/hive.dart';
import 'package:money_track/core/constants/db_constants.dart';
import 'package:money_track/core/error/failures.dart';
import 'package:money_track/core/services/connectivity_service.dart';
import 'package:money_track/data/datasources/local/category_local_datasource.dart';
import 'package:money_track/data/datasources/local/transaction_local_datasource.dart';
import 'package:money_track/data/datasources/remote/category_remote_datasource.dart';
import 'package:money_track/data/datasources/remote/transaction_remote_datasource.dart';
import 'package:money_track/data/models/firestore/category_firestore_model.dart';
import 'package:money_track/data/models/firestore/transaction_firestore_model.dart';
import 'package:money_track/data/models/sync/sync_operation_model.dart';

/// Enum for sync status
enum SyncStatus {
  idle,
  syncing,
  error,
  success,
}

/// Enum for conflict resolution strategies
enum ConflictResolutionStrategy {
  /// Last write wins based on timestamp
  lastWriteWins,

  /// Remote version always wins
  remoteWins,

  /// Local version always wins
  localWins,

  /// Higher version number wins
  versionBased,

  /// Manual resolution required (future implementation)
  manual,
}

/// Conflict information for manual resolution
class ConflictInfo {
  final String id;
  final String dataType;
  final Map<String, dynamic> localData;
  final Map<String, dynamic> remoteData;
  final DateTime localUpdatedAt;
  final DateTime remoteUpdatedAt;
  final int localVersion;
  final int remoteVersion;

  const ConflictInfo({
    required this.id,
    required this.dataType,
    required this.localData,
    required this.remoteData,
    required this.localUpdatedAt,
    required this.remoteUpdatedAt,
    required this.localVersion,
    required this.remoteVersion,
  });
}

/// Conflict resolution decision
enum ConflictResolution {
  useLocal,
  useRemote,
  requiresManualResolution,
  noConflict,
}

/// Sync error types for better error handling
enum SyncErrorType {
  networkError,
  authenticationError,
  permissionError,
  dataCorruption,
  conflictError,
  quotaExceeded,
  serverError,
  unknownError,
}

/// Detailed sync error information
class SyncError {
  final SyncErrorType type;
  final String message;
  final String? details;
  final DateTime timestamp;
  final bool isRetryable;
  final int retryCount;
  final String? operationId;

  const SyncError({
    required this.type,
    required this.message,
    this.details,
    required this.timestamp,
    this.isRetryable = true,
    this.retryCount = 0,
    this.operationId,
  });

  SyncError copyWith({
    SyncErrorType? type,
    String? message,
    String? details,
    DateTime? timestamp,
    bool? isRetryable,
    int? retryCount,
    String? operationId,
  }) {
    return SyncError(
      type: type ?? this.type,
      message: message ?? this.message,
      details: details ?? this.details,
      timestamp: timestamp ?? this.timestamp,
      isRetryable: isRetryable ?? this.isRetryable,
      retryCount: retryCount ?? this.retryCount,
      operationId: operationId ?? this.operationId,
    );
  }
}

/// Bidirectional sync result for individual items
class BidirectionalSyncResult {
  final bool localUpdated;
  final bool remoteUpdated;
  final String? error;

  const BidirectionalSyncResult({
    this.localUpdated = false,
    this.remoteUpdated = false,
    this.error,
  });
}

/// Sync progress information
class SyncProgress {
  final String currentOperation;
  final int totalItems;
  final int processedItems;
  final double progressPercentage;
  final String? currentItemId;

  const SyncProgress({
    required this.currentOperation,
    required this.totalItems,
    required this.processedItems,
    this.currentItemId,
  }) : progressPercentage =
            totalItems > 0 ? (processedItems / totalItems) * 100 : 0;

  SyncProgress copyWith({
    String? currentOperation,
    int? totalItems,
    int? processedItems,
    String? currentItemId,
  }) {
    return SyncProgress(
      currentOperation: currentOperation ?? this.currentOperation,
      totalItems: totalItems ?? this.totalItems,
      processedItems: processedItems ?? this.processedItems,
      currentItemId: currentItemId ?? this.currentItemId,
    );
  }
}

/// Sync result information
class SyncResult {
  final bool success;
  final String? error;
  final int syncedTransactions;
  final int syncedCategories;
  final int pendingOperations;
  final int failedOperations;
  final DateTime? lastSyncTime;
  final Duration? syncDuration;
  final SyncProgress? progress;
  final Map<String, dynamic>? additionalInfo;

  const SyncResult({
    required this.success,
    this.error,
    this.syncedTransactions = 0,
    this.syncedCategories = 0,
    this.pendingOperations = 0,
    this.failedOperations = 0,
    this.lastSyncTime,
    this.syncDuration,
    this.progress,
    this.additionalInfo,
  });

  /// Create a copy with updated values
  SyncResult copyWith({
    bool? success,
    String? error,
    int? syncedTransactions,
    int? syncedCategories,
    int? pendingOperations,
    int? failedOperations,
    DateTime? lastSyncTime,
    Duration? syncDuration,
    SyncProgress? progress,
    Map<String, dynamic>? additionalInfo,
  }) {
    return SyncResult(
      success: success ?? this.success,
      error: error ?? this.error,
      syncedTransactions: syncedTransactions ?? this.syncedTransactions,
      syncedCategories: syncedCategories ?? this.syncedCategories,
      pendingOperations: pendingOperations ?? this.pendingOperations,
      failedOperations: failedOperations ?? this.failedOperations,
      lastSyncTime: lastSyncTime ?? this.lastSyncTime,
      syncDuration: syncDuration ?? this.syncDuration,
      progress: progress ?? this.progress,
      additionalInfo: additionalInfo ?? this.additionalInfo,
    );
  }
}

/// Service for handling data synchronization between local and remote storage
class SyncService {
  final TransactionLocalDataSource _transactionLocalDataSource;
  final CategoryLocalDataSource _categoryLocalDataSource;
  final TransactionRemoteDataSource _transactionRemoteDataSource;
  final CategoryRemoteDataSource _categoryRemoteDataSource;
  final ConnectivityService _connectivityService;
  final HiveInterface _hive;

  // Stream controllers for sync status
  final StreamController<SyncStatus> _syncStatusController =
      StreamController<SyncStatus>.broadcast();
  final StreamController<SyncResult> _syncResultController =
      StreamController<SyncResult>.broadcast();

  // Real-time listeners
  StreamSubscription<List<TransactionFirestoreModel>>?
      _transactionStreamSubscription;
  StreamSubscription<List<CategoryFirestoreModel>>? _categoryStreamSubscription;
  StreamSubscription<bool>? _connectivitySubscription;

  SyncStatus _currentStatus = SyncStatus.idle;
  String? _currentUserId;
  Timer? _periodicSyncTimer;

  // Sync tracking fields
  DateTime? _lastSyncTime;
  int _totalSyncedTransactions = 0;
  int _totalSyncedCategories = 0;
  int _totalFailedOperations = 0;
  final Map<String, int> _syncSessionStats = {
    'transactions': 0,
    'categories': 0,
    'failed': 0,
  };

  // Conflict resolution configuration
  ConflictResolutionStrategy _conflictResolutionStrategy =
      ConflictResolutionStrategy.lastWriteWins;
  final List<ConflictInfo> _pendingConflicts = [];
  final StreamController<List<ConflictInfo>> _conflictController =
      StreamController<List<ConflictInfo>>.broadcast();

  // Offline/Online state management
  bool _wasOffline = false;
  DateTime? _lastOnlineTime;
  DateTime? _lastOfflineTime;
  Timer? _retryTimer;
  int _consecutiveFailures = 0;
  final int _maxRetryAttempts = 5;
  final Duration _baseRetryDelay = const Duration(seconds: 30);
  final StreamController<bool> _connectivityStatusController =
      StreamController<bool>.broadcast();

  // Progress tracking
  SyncProgress? _currentProgress;
  final StreamController<SyncProgress> _progressController =
      StreamController<SyncProgress>.broadcast();

  // Error handling and tracking
  final List<SyncError> _recentErrors = [];
  final StreamController<SyncError> _errorController =
      StreamController<SyncError>.broadcast();
  final int _maxErrorHistory = 50;

  // Performance optimization constants
  static const int _batchSize = 20; // Process items in batches
  static const int _yieldInterval = 5; // Yield to UI thread every N items
  static const Duration _progressUpdateThrottle =
      Duration(milliseconds: 100); // Throttle progress updates
  DateTime _lastProgressUpdate = DateTime.now();

  // Memory optimization - object pooling for frequently created objects
  final List<Map<String, dynamic>> _mapPool = [];
  final List<List<dynamic>> _listPool = [];
  static const int _maxPoolSize = 10;

  SyncService({
    required TransactionLocalDataSource transactionLocalDataSource,
    required CategoryLocalDataSource categoryLocalDataSource,
    required TransactionRemoteDataSource transactionRemoteDataSource,
    required CategoryRemoteDataSource categoryRemoteDataSource,
    required ConnectivityService connectivityService,
    required HiveInterface hive,
  })  : _transactionLocalDataSource = transactionLocalDataSource,
        _categoryLocalDataSource = categoryLocalDataSource,
        _transactionRemoteDataSource = transactionRemoteDataSource,
        _categoryRemoteDataSource = categoryRemoteDataSource,
        _connectivityService = connectivityService,
        _hive = hive;

  /// Current sync status
  SyncStatus get currentStatus => _currentStatus;

  /// Stream of sync status changes
  Stream<SyncStatus> get syncStatusStream => _syncStatusController.stream;

  /// Stream of sync results
  Stream<SyncResult> get syncResultStream => _syncResultController.stream;

  /// Stream of pending conflicts (for manual resolution)
  Stream<List<ConflictInfo>> get conflictStream => _conflictController.stream;

  /// Current conflict resolution strategy
  ConflictResolutionStrategy get conflictResolutionStrategy =>
      _conflictResolutionStrategy;

  /// Set conflict resolution strategy
  void setConflictResolutionStrategy(ConflictResolutionStrategy strategy) {
    _conflictResolutionStrategy = strategy;
    log('Conflict resolution strategy changed to: ${strategy.name}',
        name: 'SyncService');
  }

  /// Get pending conflicts
  List<ConflictInfo> get pendingConflicts =>
      List.unmodifiable(_pendingConflicts);

  /// Resolve a conflict manually by choosing local or remote version
  Future<void> resolveConflictManually(String conflictId, bool useLocal) async {
    try {
      final conflictIndex =
          _pendingConflicts.indexWhere((c) => c.id == conflictId);
      if (conflictIndex == -1) {
        log('Conflict not found: $conflictId', name: 'SyncService');
        return;
      }

      final conflict = _pendingConflicts[conflictIndex];

      if (conflict.dataType == 'transaction') {
        if (useLocal) {
          final localTransaction =
              TransactionFirestoreModel.fromMap(conflict.localData);
          final updatedTransaction = localTransaction.copyWith(
            version: localTransaction.version + 1,
            updatedAt: DateTime.now(),
          );
          await _transactionRemoteDataSource
              .updateTransaction(updatedTransaction);
        } else {
          final remoteTransaction =
              TransactionFirestoreModel.fromMap(conflict.remoteData);
          await _transactionLocalDataSource
              .editTransaction(remoteTransaction.toHiveModel());
        }
      } else if (conflict.dataType == 'category') {
        if (useLocal) {
          final localCategory =
              CategoryFirestoreModel.fromMap(conflict.localData);
          final updatedCategory = localCategory.copyWith(
            version: localCategory.version + 1,
            updatedAt: DateTime.now(),
          );
          await _categoryRemoteDataSource.updateCategory(updatedCategory);
        } else {
          final remoteCategory =
              CategoryFirestoreModel.fromMap(conflict.remoteData);
          await _categoryLocalDataSource
              .addCategory(remoteCategory.toHiveModel());
        }
      }

      // Remove resolved conflict
      _pendingConflicts.removeAt(conflictIndex);
      _conflictController.add(List.from(_pendingConflicts));

      log('Manually resolved conflict: $conflictId (used ${useLocal ? 'local' : 'remote'})',
          name: 'SyncService');
    } catch (e) {
      log('Error resolving conflict manually: $e', name: 'SyncService');
      rethrow;
    }
  }

  /// Clear all pending conflicts
  void clearPendingConflicts() {
    _pendingConflicts.clear();
    _conflictController.add(List.from(_pendingConflicts));
    log('Cleared all pending conflicts', name: 'SyncService');
  }

  /// Stream of connectivity status changes
  Stream<bool> get connectivityStatusStream =>
      _connectivityStatusController.stream;

  /// Get offline/online statistics
  Map<String, dynamic> get connectivityStats => {
        'isOnline': _connectivityService.isConnected,
        'wasOffline': _wasOffline,
        'lastOnlineTime': _lastOnlineTime?.toIso8601String(),
        'lastOfflineTime': _lastOfflineTime?.toIso8601String(),
        'consecutiveFailures': _consecutiveFailures,
        'maxRetryAttempts': _maxRetryAttempts,
      };

  /// Stream of sync progress updates
  Stream<SyncProgress> get progressStream => _progressController.stream;

  /// Current sync progress
  SyncProgress? get currentProgress => _currentProgress;

  /// Stream of sync errors
  Stream<SyncError> get errorStream => _errorController.stream;

  /// Get recent sync errors
  List<SyncError> get recentErrors => List.unmodifiable(_recentErrors);

  /// Clear error history
  void clearErrorHistory() {
    _recentErrors.clear();
    log('Cleared sync error history', name: 'SyncService');
  }

  /// Initialize sync service for a user
  Future<void> initializeForUser(String userId) async {
    try {
      log('Initializing sync service for user: $userId', name: 'SyncService');

      _currentUserId = userId;

      // Listen to connectivity changes
      _connectivitySubscription =
          _connectivityService.connectivityStream.listen(
        _onConnectivityChanged,
        onError: (error) {
          log('Connectivity stream error: $error', name: 'SyncService');
        },
      );

      // Start real-time listeners if connected
      if (_connectivityService.isConnected) {
        await _startRealTimeListeners();
        await _performInitialSync();
      }

      // Start periodic sync (every 5 minutes)
      _periodicSyncTimer = Timer.periodic(
        const Duration(minutes: 5),
        (_) => _performPeriodicSync(),
      );
    } catch (e) {
      log('Failed to initialize sync service: $e', name: 'SyncService');
      _updateSyncStatus(SyncStatus.error);
    }
  }

  /// Perform initial sync when user logs in
  Future<SyncResult> _performInitialSync() async {
    if (_currentUserId == null) {
      return const SyncResult(success: false, error: 'No user logged in');
    }

    log('Performing initial sync for user: $_currentUserId',
        name: 'SyncService');
    _updateSyncStatus(SyncStatus.syncing);

    final syncStartTime = DateTime.now();
    _resetSyncSessionStats();

    try {
      // First, sync from remote to local (download user's data)
      await _syncFromRemoteToLocal();

      // Then, sync any pending local changes to remote
      await _syncPendingOperations();

      final syncEndTime = DateTime.now();
      final syncDuration = syncEndTime.difference(syncStartTime);
      _lastSyncTime = syncEndTime;

      _clearProgress();

      final result = SyncResult(
        success: true,
        syncedTransactions: _syncSessionStats['transactions']!,
        syncedCategories: _syncSessionStats['categories']!,
        failedOperations: _syncSessionStats['failed']!,
        pendingOperations: await _getPendingOperationsCount(),
        lastSyncTime: _lastSyncTime,
        syncDuration: syncDuration,
        additionalInfo: {
          'syncType': 'initial',
          'totalItems': _syncSessionStats['transactions']! +
              _syncSessionStats['categories']!,
          'offlineRecovery': _wasOffline,
        },
      );

      // Update total counters
      _totalSyncedTransactions += _syncSessionStats['transactions']!;
      _totalSyncedCategories += _syncSessionStats['categories']!;
      _totalFailedOperations += _syncSessionStats['failed']!;

      _updateSyncStatus(SyncStatus.success);
      _syncResultController.add(result);

      log('Initial sync completed: ${_syncSessionStats['transactions']} transactions, ${_syncSessionStats['categories']} categories in ${syncDuration.inMilliseconds}ms',
          name: 'SyncService');

      return result;
    } catch (e) {
      log('Initial sync failed: $e', name: 'SyncService');
      _updateSyncStatus(SyncStatus.error);

      final syncEndTime = DateTime.now();
      final syncDuration = syncEndTime.difference(syncStartTime);

      final result = SyncResult(
        success: false,
        error: e.toString(),
        syncedTransactions: _syncSessionStats['transactions']!,
        syncedCategories: _syncSessionStats['categories']!,
        failedOperations: _syncSessionStats['failed']! + 1,
        pendingOperations: await _getPendingOperationsCount(),
        lastSyncTime: _lastSyncTime,
        syncDuration: syncDuration,
      );

      _syncResultController.add(result);
      return result;
    }
  }

  /// Sync data from remote to local storage with enhanced bidirectional logic
  Future<void> _syncFromRemoteToLocal() async {
    if (_currentUserId == null) return;

    try {
      // Perform bidirectional sync for categories and transactions
      await _performBidirectionalCategorySync();
      await _performBidirectionalTransactionSync();
    } catch (e) {
      log('Failed to sync from remote to local: $e', name: 'SyncService');
      _syncSessionStats['failed'] = _syncSessionStats['failed']! + 1;
      rethrow;
    }
  }

  /// Optimized bidirectional category synchronization with batch processing
  Future<void> _performBidirectionalCategorySync() async {
    try {
      // Get local and remote data
      final localCategories = await _categoryLocalDataSource.getAllCategories();
      final remoteCategories =
          await _categoryRemoteDataSource.getAllCategories(_currentUserId!);

      // Use pooled objects to reduce memory allocations
      final localCategoryMap = _getPooledMap();
      final remoteCategoryMap = _getPooledMap();

      // Build maps efficiently
      for (final cat in localCategories) {
        localCategoryMap[cat.id] = cat;
      }
      for (final cat in remoteCategories) {
        remoteCategoryMap[cat.id] = cat;
      }

      // Pre-filter local categories that need uploading
      final localCategoriesToUpload = _getPooledList();
      for (final cat in localCategories) {
        if (!remoteCategoryMap.containsKey(cat.id)) {
          localCategoriesToUpload.add(cat);
        }
      }

      final totalItems =
          remoteCategories.length + localCategoriesToUpload.length;
      int syncedFromRemote = 0;
      int syncedToRemote = 0;

      // Process remote categories in batches
      await _processBatch(
        remoteCategories,
        'Syncing categories from remote',
        (remoteCategory, index) async {
          final localCategory = localCategoryMap[remoteCategory.id];

          if (localCategory == null) {
            // New category from remote - add to local
            await _categoryLocalDataSource
                .addCategory(remoteCategory.toHiveModel());
            syncedFromRemote++;
          } else {
            // Category exists locally - check for conflicts and resolve
            final syncResult = await _resolveCategoryConflictBidirectional(
                localCategory, remoteCategory);
            if (syncResult.localUpdated) syncedFromRemote++;
            if (syncResult.remoteUpdated) syncedToRemote++;
          }
        },
      );

      // Process local categories that need uploading in batches
      await _processBatch(
        localCategoriesToUpload,
        'Uploading categories to remote',
        (localCategory, index) async {
          // Convert to Firestore model once
          final firestoreCategory = CategoryFirestoreModel.fromHiveModel(
              localCategory, _currentUserId!);
          await _categoryRemoteDataSource.addCategory(firestoreCategory);
          syncedToRemote++;
        },
      );

      // Update session stats atomically
      _syncSessionStats['categories'] = (_syncSessionStats['categories']! +
          syncedFromRemote +
          syncedToRemote);

      log('Category bidirectional sync completed: $syncedFromRemote from remote, $syncedToRemote to remote',
          name: 'SyncService');

      // Return pooled objects for reuse
      _returnMapToPool(localCategoryMap);
      _returnMapToPool(remoteCategoryMap);
      _returnListToPool(localCategoriesToUpload);
    } catch (e) {
      log('Error in bidirectional category sync: $e', name: 'SyncService');

      // Ensure cleanup even on error
      try {
        _returnMapToPool(localCategoryMap);
        _returnMapToPool(remoteCategoryMap);
        _returnListToPool(localCategoriesToUpload);
      } catch (_) {
        // Ignore cleanup errors
      }

      rethrow;
    }
  }

  /// Optimized bidirectional transaction synchronization with batch processing
  Future<void> _performBidirectionalTransactionSync() async {
    // Declare pooled objects at method level for proper scope
    late final Map<String, dynamic> localTransactionMap;
    late final Map<String, dynamic> remoteTransactionMap;
    late final List<dynamic> localTransactionsToUpload;

    try {
      // Get local and remote data
      final localTransactions =
          await _transactionLocalDataSource.getAllTransactions();
      final remoteTransactions = await _transactionRemoteDataSource
          .getAllTransactions(_currentUserId!);

      // Use pooled objects to reduce memory allocations
      localTransactionMap = _getPooledMap();
      remoteTransactionMap = _getPooledMap();

      // Build maps with null checks
      for (final trans in localTransactions) {
        if (trans.id != null) {
          localTransactionMap[trans.id!] = trans;
        }
      }
      for (final trans in remoteTransactions) {
        remoteTransactionMap[trans.id] = trans;
      }

      // Pre-filter local transactions that need uploading
      localTransactionsToUpload = _getPooledList();
      for (final trans in localTransactions) {
        if (trans.id != null && !remoteTransactionMap.containsKey(trans.id)) {
          localTransactionsToUpload.add(trans);
        }
      }

      int syncedFromRemote = 0;
      int syncedToRemote = 0;

      // Process remote transactions in batches
      await _processBatch(
        remoteTransactions,
        'Syncing transactions from remote',
        (remoteTransaction, index) async {
          final localTransaction = localTransactionMap[remoteTransaction.id];

          if (localTransaction == null) {
            // New transaction from remote - add to local
            await _transactionLocalDataSource
                .addTransaction(remoteTransaction.toHiveModel());
            syncedFromRemote++;
          } else {
            // Transaction exists locally - check for conflicts and resolve
            final syncResult = await _resolveTransactionConflictBidirectional(
                localTransaction, remoteTransaction);
            if (syncResult.localUpdated) syncedFromRemote++;
            if (syncResult.remoteUpdated) syncedToRemote++;
          }
        },
      );

      // Process local transactions that need uploading in batches
      await _processBatch(
        localTransactionsToUpload,
        'Uploading transactions to remote',
        (localTransaction, index) async {
          // Convert to Firestore model once
          final firestoreTransaction = TransactionFirestoreModel.fromHiveModel(
              localTransaction, _currentUserId!);
          await _transactionRemoteDataSource
              .addTransaction(firestoreTransaction);
          syncedToRemote++;
        },
      );

      // Update session stats atomically
      _syncSessionStats['transactions'] = (_syncSessionStats['transactions']! +
          syncedFromRemote +
          syncedToRemote);

      log('Transaction bidirectional sync completed: $syncedFromRemote from remote, $syncedToRemote to remote',
          name: 'SyncService');

      // Return pooled objects for reuse
      _returnMapToPool(localTransactionMap);
      _returnMapToPool(remoteTransactionMap);
      _returnListToPool(localTransactionsToUpload);
    } catch (e) {
      log('Error in bidirectional transaction sync: $e', name: 'SyncService');

      // Ensure cleanup even on error
      try {
        _returnMapToPool(localTransactionMap);
        _returnMapToPool(remoteTransactionMap);
        _returnListToPool(localTransactionsToUpload);
      } catch (_) {
        // Ignore cleanup errors
      }

      rethrow;
    }
  }

  /// Start real-time listeners for remote data changes
  Future<void> _startRealTimeListeners() async {
    if (_currentUserId == null) return;

    try {
      // Listen to transaction changes
      _transactionStreamSubscription = _transactionRemoteDataSource
          .getTransactionsStream(_currentUserId!)
          .listen(
        _onRemoteTransactionsChanged,
        onError: (error) {
          log('Transaction stream error: $error', name: 'SyncService');
        },
      );

      // Listen to category changes
      _categoryStreamSubscription =
          _categoryRemoteDataSource.getCategoriesStream(_currentUserId!).listen(
        _onRemoteCategoriesChanged,
        onError: (error) {
          log('Category stream error: $error', name: 'SyncService');
        },
      );

      log('Started real-time listeners', name: 'SyncService');
    } catch (e) {
      log('Failed to start real-time listeners: $e', name: 'SyncService');
    }
  }

  /// Optimized handler for remote transaction changes with batch processing
  void _onRemoteTransactionsChanged(
      List<TransactionFirestoreModel> remoteTransactions) async {
    if (remoteTransactions.isEmpty) return;

    try {
      // Get all local transactions once to avoid repeated database queries
      final localTransactions =
          await _transactionLocalDataSource.getAllTransactions();

      // Use pooled map for efficient lookups
      final localTransactionMap = _getPooledMap();

      try {
        // Build lookup map once
        for (final trans in localTransactions) {
          if (trans.id != null) {
            localTransactionMap[trans.id!] = trans;
          }
        }

        // Process remote changes in batches with UI yielding
        await _processBatch(
          remoteTransactions,
          'Processing remote transaction changes',
          (remoteTransaction, index) async {
            final localTransaction = localTransactionMap[remoteTransaction.id];

            if (localTransaction == null) {
              // New transaction from remote
              await _transactionLocalDataSource
                  .addTransaction(remoteTransaction.toHiveModel());
            } else {
              // Check for conflicts and resolve
              await _resolveTransactionConflict(
                  localTransaction, remoteTransaction);
            }
          },
        );
      } finally {
        // Always return pooled object
        _returnMapToPool(localTransactionMap);
      }
    } catch (e) {
      log('Error handling remote transaction changes: $e', name: 'SyncService');
    }
  }

  /// Optimized handler for remote category changes with batch processing
  void _onRemoteCategoriesChanged(
      List<CategoryFirestoreModel> remoteCategories) async {
    if (remoteCategories.isEmpty) return;

    try {
      // Get all local categories once to avoid repeated database queries
      final localCategories = await _categoryLocalDataSource.getAllCategories();

      // Use pooled map for efficient lookups
      final localCategoryMap = _getPooledMap();

      try {
        // Build lookup map once
        for (final cat in localCategories) {
          localCategoryMap[cat.id] = cat;
        }

        // Process remote changes in batches with UI yielding
        await _processBatch(
          remoteCategories,
          'Processing remote category changes',
          (remoteCategory, index) async {
            final localCategory = localCategoryMap[remoteCategory.id];

            if (localCategory == null) {
              // New category from remote
              await _categoryLocalDataSource
                  .addCategory(remoteCategory.toHiveModel());
            } else {
              // Check for conflicts and resolve
              await _resolveCategoryConflict(localCategory, remoteCategory);
            }
          },
        );
      } finally {
        // Always return pooled object
        _returnMapToPool(localCategoryMap);
      }
    } catch (e) {
      log('Error handling remote category changes: $e', name: 'SyncService');
    }
  }

  /// Handle connectivity changes with enhanced offline/online state management
  void _onConnectivityChanged(bool isConnected) async {
    final now = DateTime.now();

    log('Connectivity changed: ${isConnected ? "Connected" : "Disconnected"}',
        name: 'SyncService');

    // Update connectivity status stream
    _connectivityStatusController.add(isConnected);

    if (isConnected) {
      // Coming back online
      if (_wasOffline) {
        final offlineDuration = _lastOfflineTime != null
            ? now.difference(_lastOfflineTime!)
            : Duration.zero;
        log('Back online after ${offlineDuration.inMinutes} minutes offline',
            name: 'SyncService');
      }

      _lastOnlineTime = now;
      _wasOffline = false;

      if (_currentUserId != null) {
        // Cancel any pending retry timer
        _retryTimer?.cancel();
        _retryTimer = null;

        try {
          // Start real-time listeners
          await _startRealTimeListeners();

          // Perform comprehensive sync when coming back online
          await _performOnlineRecoverySync();

          // Reset consecutive failures on successful reconnection
          _consecutiveFailures = 0;
        } catch (e) {
          log('Error during online recovery: $e', name: 'SyncService');

          // Categorize and handle the error
          final syncError =
              _categorizeError(e, retryCount: _consecutiveFailures);
          _handleSyncError(syncError);

          _consecutiveFailures++;

          // Only schedule retry if error is retryable
          if (syncError.isRetryable) {
            _scheduleRetrySync();
          } else {
            log('Non-retryable error encountered, stopping retry attempts',
                name: 'SyncService');
          }
        }
      }
    } else {
      // Going offline
      _lastOfflineTime = now;
      _wasOffline = true;

      // Stop real-time listeners to conserve resources
      await _stopRealTimeListeners();

      log('Went offline - sync operations will be queued', name: 'SyncService');
    }
  }

  /// Stop real-time listeners
  Future<void> _stopRealTimeListeners() async {
    await _transactionStreamSubscription?.cancel();
    await _categoryStreamSubscription?.cancel();
    _transactionStreamSubscription = null;
    _categoryStreamSubscription = null;
    log('Stopped real-time listeners', name: 'SyncService');
  }

  /// Perform comprehensive sync when coming back online
  Future<void> _performOnlineRecoverySync() async {
    if (_currentUserId == null) return;

    log('Performing online recovery sync', name: 'SyncService');
    _updateSyncStatus(SyncStatus.syncing);

    try {
      // First, sync any pending local operations to remote
      await _syncPendingOperations();

      // Then, perform full bidirectional sync to catch up on remote changes
      await _syncFromRemoteToLocal();

      // Get final counts for reporting
      final pendingCount = await _getPendingOperationsCount();

      log('Online recovery sync completed. Pending operations: $pendingCount',
          name: 'SyncService');

      _updateSyncStatus(SyncStatus.success);
    } catch (e) {
      log('Online recovery sync failed: $e', name: 'SyncService');
      _updateSyncStatus(SyncStatus.error);
      rethrow;
    }
  }

  /// Schedule retry sync with exponential backoff
  void _scheduleRetrySync() {
    if (_consecutiveFailures >= _maxRetryAttempts) {
      log('Max retry attempts reached. Stopping retry attempts.',
          name: 'SyncService');
      return;
    }

    // Cancel existing timer
    _retryTimer?.cancel();

    // Calculate delay with exponential backoff
    final delayMultiplier = math.pow(2, _consecutiveFailures).toInt();
    final delaySeconds =
        (_baseRetryDelay.inSeconds * delayMultiplier).clamp(30, 300);
    final delay = Duration(seconds: delaySeconds);

    log('Scheduling retry sync in ${delay.inSeconds} seconds (attempt ${_consecutiveFailures + 1}/$_maxRetryAttempts)',
        name: 'SyncService');

    _retryTimer = Timer(delay, () async {
      if (_connectivityService.isConnected && _currentUserId != null) {
        try {
          await _performOnlineRecoverySync();
          _consecutiveFailures = 0; // Reset on success
        } catch (e) {
          log('Retry sync failed: $e', name: 'SyncService');

          // Categorize and handle the error
          final syncError =
              _categorizeError(e, retryCount: _consecutiveFailures);
          _handleSyncError(syncError);

          _consecutiveFailures++;

          // Only schedule next retry if error is retryable
          if (syncError.isRetryable) {
            _scheduleRetrySync(); // Schedule next retry
          } else {
            log('Non-retryable error in retry sync, stopping retries',
                name: 'SyncService');
          }
        }
      } else {
        log('Still offline, skipping retry sync', name: 'SyncService');
      }
    });
  }

  /// Update sync status and notify listeners
  void _updateSyncStatus(SyncStatus status) {
    _currentStatus = status;
    _syncStatusController.add(status);
  }

  /// Reset sync session statistics
  void _resetSyncSessionStats() {
    _syncSessionStats['transactions'] = 0;
    _syncSessionStats['categories'] = 0;
    _syncSessionStats['failed'] = 0;
  }

  /// Update sync progress with throttling to reduce UI thread pressure
  void _updateProgress(SyncProgress progress) {
    final now = DateTime.now();

    // Throttle progress updates to reduce stream controller overhead
    if (now.difference(_lastProgressUpdate) < _progressUpdateThrottle &&
        progress.processedItems < progress.totalItems) {
      return;
    }

    _lastProgressUpdate = now;
    _currentProgress = progress;
    _progressController.add(progress);

    // Reduce logging frequency for performance
    if (progress.processedItems % 10 == 0 ||
        progress.processedItems == progress.totalItems) {
      log('Sync progress: ${progress.currentOperation} (${progress.processedItems}/${progress.totalItems}) ${progress.progressPercentage.toStringAsFixed(1)}%',
          name: 'SyncService');
    }
  }

  /// Optimized progress update for batch operations
  void _updateProgressBatch(
      String operation, int totalItems, int processedItems,
      {String? currentItemId}) {
    // Only create new SyncProgress object when necessary
    if (_currentProgress == null ||
        _currentProgress!.totalItems != totalItems ||
        _currentProgress!.currentOperation != operation ||
        DateTime.now().difference(_lastProgressUpdate) >=
            _progressUpdateThrottle) {
      _updateProgress(SyncProgress(
        currentOperation: operation,
        totalItems: totalItems,
        processedItems: processedItems,
        currentItemId: currentItemId,
      ));
    }
  }

  /// Clear current progress
  void _clearProgress() {
    _currentProgress = null;
  }

  /// Get a reusable map from pool to reduce allocations
  Map<String, dynamic> _getPooledMap() {
    if (_mapPool.isNotEmpty) {
      final map = _mapPool.removeLast();
      map.clear();
      return map;
    }
    return <String, dynamic>{};
  }

  /// Return map to pool for reuse
  void _returnMapToPool(Map<String, dynamic> map) {
    if (_mapPool.length < _maxPoolSize) {
      map.clear();
      _mapPool.add(map);
    }
  }

  /// Get a reusable list from pool to reduce allocations
  List<dynamic> _getPooledList() {
    if (_listPool.isNotEmpty) {
      final list = _listPool.removeLast();
      list.clear();
      return list;
    }
    return <dynamic>[];
  }

  /// Return list to pool for reuse
  void _returnListToPool(List<dynamic> list) {
    if (_listPool.length < _maxPoolSize) {
      list.clear();
      _listPool.add(list);
    }
  }

  /// Yield to UI thread to prevent blocking
  Future<void> _yieldToUI() async {
    await Future.delayed(Duration.zero);
  }

  /// Process items in batches with UI thread yielding
  Future<void> _processBatch<T>(
    List<T> items,
    String operationName,
    Future<void> Function(T item, int index) processor,
  ) async {
    final totalItems = items.length;

    for (int i = 0; i < items.length; i++) {
      // Update progress in batches to reduce overhead
      if (i % _yieldInterval == 0) {
        _updateProgressBatch(operationName, totalItems, i);
        await _yieldToUI(); // Yield to UI thread
      }

      await processor(items[i], i);
    }

    // Final progress update
    _updateProgressBatch(operationName, totalItems, totalItems);
  }

  /// Process large sync operations with memory optimization
  Future<void> _processLargeSyncOperation(
      String operationName, Future<void> Function() operation) async {
    try {
      log('Starting large sync operation: $operationName', name: 'SyncService');

      // Update progress to indicate start
      _updateProgress(SyncProgress(
        currentOperation: operationName,
        totalItems: 1,
        processedItems: 0,
      ));

      // Execute the operation
      await operation();

      // Update progress to indicate completion
      _updateProgress(SyncProgress(
        currentOperation: operationName,
        totalItems: 1,
        processedItems: 1,
      ));

      log('Completed large sync operation: $operationName',
          name: 'SyncService');
    } catch (e) {
      log('Error in large sync operation $operationName: $e',
          name: 'SyncService');
      rethrow;
    }
  }

  /// Categorize and handle sync errors
  SyncError _categorizeError(dynamic error,
      {String? operationId, int retryCount = 0}) {
    SyncErrorType errorType;
    bool isRetryable = true;
    String message = error.toString();
    String? details;

    if (error is NetworkFailure) {
      errorType = SyncErrorType.networkError;
      message = 'Network connection failed';
      details = error.message;
    } else if (error is DatabaseFailure) {
      errorType = SyncErrorType.dataCorruption;
      message = 'Local database error';
      details = error.message;
    } else if (error.toString().contains('permission') ||
        error.toString().contains('unauthorized')) {
      errorType = SyncErrorType.permissionError;
      message = 'Permission denied';
      isRetryable = false;
    } else if (error.toString().contains('authentication') ||
        error.toString().contains('auth')) {
      errorType = SyncErrorType.authenticationError;
      message = 'Authentication failed';
      isRetryable = false;
    } else if (error.toString().contains('quota') ||
        error.toString().contains('limit')) {
      errorType = SyncErrorType.quotaExceeded;
      message = 'Storage quota exceeded';
      isRetryable = false;
    } else if (error.toString().contains('server') ||
        error.toString().contains('500')) {
      errorType = SyncErrorType.serverError;
      message = 'Server error occurred';
    } else {
      errorType = SyncErrorType.unknownError;
      message = 'Unknown sync error';
    }

    return SyncError(
      type: errorType,
      message: message,
      details: details,
      timestamp: DateTime.now(),
      isRetryable: isRetryable,
      retryCount: retryCount,
      operationId: operationId,
    );
  }

  /// Record and handle sync error
  void _handleSyncError(SyncError error) {
    // Add to error history
    _recentErrors.add(error);

    // Keep only recent errors
    if (_recentErrors.length > _maxErrorHistory) {
      _recentErrors.removeAt(0);
    }

    // Notify error stream listeners
    _errorController.add(error);

    log('Sync error [${error.type.name}]: ${error.message}${error.details != null ? ' - ${error.details}' : ''}',
        name: 'SyncService');

    // Handle specific error types
    switch (error.type) {
      case SyncErrorType.authenticationError:
        log('Authentication error - user may need to re-login',
            name: 'SyncService');
        break;
      case SyncErrorType.permissionError:
        log('Permission error - check user permissions', name: 'SyncService');
        break;
      case SyncErrorType.quotaExceeded:
        log('Quota exceeded - sync operations suspended', name: 'SyncService');
        break;
      case SyncErrorType.networkError:
        log('Network error - will retry when connection is restored',
            name: 'SyncService');
        break;
      default:
        break;
    }
  }

  /// Resolve conflict using the configured strategy
  ConflictResolution _resolveConflictWithStrategy(ConflictInfo conflictInfo) {
    switch (_conflictResolutionStrategy) {
      case ConflictResolutionStrategy.lastWriteWins:
        if (conflictInfo.remoteUpdatedAt.isAfter(conflictInfo.localUpdatedAt)) {
          return ConflictResolution.useRemote;
        } else if (conflictInfo.localUpdatedAt
            .isAfter(conflictInfo.remoteUpdatedAt)) {
          return ConflictResolution.useLocal;
        } else {
          // Same timestamp, use version as tiebreaker
          return conflictInfo.remoteVersion > conflictInfo.localVersion
              ? ConflictResolution.useRemote
              : ConflictResolution.useLocal;
        }

      case ConflictResolutionStrategy.remoteWins:
        return ConflictResolution.useRemote;

      case ConflictResolutionStrategy.localWins:
        return ConflictResolution.useLocal;

      case ConflictResolutionStrategy.versionBased:
        if (conflictInfo.remoteVersion > conflictInfo.localVersion) {
          return ConflictResolution.useRemote;
        } else if (conflictInfo.localVersion > conflictInfo.remoteVersion) {
          return ConflictResolution.useLocal;
        } else {
          // Same version, use timestamp as tiebreaker
          return conflictInfo.remoteUpdatedAt
                  .isAfter(conflictInfo.localUpdatedAt)
              ? ConflictResolution.useRemote
              : ConflictResolution.useLocal;
        }

      case ConflictResolutionStrategy.manual:
        return ConflictResolution.requiresManualResolution;
    }
  }

  /// Get count of pending sync operations
  Future<int> _getPendingOperationsCount() async {
    try {
      final syncBox =
          await _hive.openBox<SyncOperationModel>('sync-operations');
      return syncBox.values.length;
    } catch (e) {
      log('Error getting pending operations count: $e', name: 'SyncService');
      return 0;
    }
  }

  /// Perform periodic sync
  void _performPeriodicSync() async {
    if (_connectivityService.isConnected && _currentUserId != null) {
      await _syncPendingOperations();
    }
  }

  /// Sync pending operations to remote
  Future<void> _syncPendingOperations() async {
    if (_currentUserId == null) return;

    try {
      final syncBox =
          await _hive.openBox<SyncOperationModel>('sync-operations');
      final pendingOperations =
          syncBox.values.where((op) => op.userId == _currentUserId).toList();

      if (pendingOperations.isEmpty) return;

      log('Syncing ${pendingOperations.length} pending operations',
          name: 'SyncService');

      int successfulOperations = 0;
      int failedOperations = 0;

      // Process pending operations in batches with UI thread yielding
      await _processBatch(
        pendingOperations,
        'Syncing pending operations',
        (operation, index) async {
          try {
            await _executeSyncOperation(operation);
            await syncBox.delete(operation.id);
            successfulOperations++;

            // Update session stats based on operation type
            if (operation.dataType == SyncDataType.transaction) {
              _syncSessionStats['transactions'] =
                  _syncSessionStats['transactions']! + 1;
            } else if (operation.dataType == SyncDataType.category) {
              _syncSessionStats['categories'] =
                  _syncSessionStats['categories']! + 1;
            }
          } catch (e) {
            failedOperations++;

            // Categorize the error
            final syncError = _categorizeError(e,
                operationId: operation.id, retryCount: operation.retryCount);
            _handleSyncError(syncError);

            // Update retry count
            final updatedOperation = operation.copyWith(
              retryCount: operation.retryCount + 1,
              error: e.toString(),
            );

            // Check if error is retryable and hasn't exceeded max retries
            if (!syncError.isRetryable || updatedOperation.retryCount >= 3) {
              await syncBox.delete(operation.id);
              _syncSessionStats['failed'] = _syncSessionStats['failed']! + 1;
            } else {
              await syncBox.put(operation.id, updatedOperation);
            }
          }
        },
      );

      log('Completed pending operations sync: $successfulOperations successful, $failedOperations failed',
          name: 'SyncService');
    } catch (e) {
      log('Error syncing pending operations: $e', name: 'SyncService');
      _syncSessionStats['failed'] = _syncSessionStats['failed']! + 1;
    }
  }

  /// Execute a sync operation
  Future<void> _executeSyncOperation(SyncOperationModel operation) async {
    switch (operation.dataType) {
      case SyncDataType.transaction:
        await _executeTransactionOperation(operation);
        break;
      case SyncDataType.category:
        await _executeCategoryOperation(operation);
        break;
    }
  }

  /// Execute transaction sync operation
  Future<void> _executeTransactionOperation(
      SyncOperationModel operation) async {
    switch (operation.operationType) {
      case SyncOperationType.create:
      case SyncOperationType.update:
        // Convert DateTime objects back to Timestamps for Firestore
        final firestoreData = operation.getDataForFirestore();
        final transaction = TransactionFirestoreModel.fromMap(firestoreData);
        if (operation.operationType == SyncOperationType.create) {
          await _transactionRemoteDataSource.addTransaction(transaction);
        } else {
          await _transactionRemoteDataSource.updateTransaction(transaction);
        }
        break;
      case SyncOperationType.delete:
        await _transactionRemoteDataSource.deleteTransaction(
            operation.dataId, operation.userId);
        break;
    }
  }

  /// Execute category sync operation
  Future<void> _executeCategoryOperation(SyncOperationModel operation) async {
    switch (operation.operationType) {
      case SyncOperationType.create:
      case SyncOperationType.update:
        // Convert DateTime objects back to Timestamps for Firestore
        final firestoreData = operation.getDataForFirestore();
        final category = CategoryFirestoreModel.fromMap(firestoreData);
        if (operation.operationType == SyncOperationType.create) {
          await _categoryRemoteDataSource.addCategory(category);
        } else {
          await _categoryRemoteDataSource.updateCategory(category);
        }
        break;
      case SyncOperationType.delete:
        await _categoryRemoteDataSource.deleteCategory(
            operation.dataId, operation.userId);
        break;
    }
  }

  /// Enhanced bidirectional transaction conflict resolution
  Future<BidirectionalSyncResult> _resolveTransactionConflictBidirectional(
      dynamic localTransaction,
      TransactionFirestoreModel remoteTransaction) async {
    try {
      // Convert local transaction to Firestore model for comparison
      final localFirestoreTransaction = TransactionFirestoreModel.fromHiveModel(
          localTransaction, _currentUserId!);

      // Create conflict info for strategy-based resolution
      final conflictInfo = ConflictInfo(
        id: remoteTransaction.id,
        dataType: 'transaction',
        localData: localFirestoreTransaction.toFirestore(),
        remoteData: remoteTransaction.toFirestore(),
        localUpdatedAt: localFirestoreTransaction.updatedAt,
        remoteUpdatedAt: remoteTransaction.updatedAt,
        localVersion: localFirestoreTransaction.version,
        remoteVersion: remoteTransaction.version,
      );

      // Apply conflict resolution strategy
      final resolution = _resolveConflictWithStrategy(conflictInfo);

      switch (resolution) {
        case ConflictResolution.useLocal:
          final updatedLocalTransaction = localFirestoreTransaction.copyWith(
            version: localFirestoreTransaction.version + 1,
            updatedAt: DateTime.now(),
          );
          await _transactionRemoteDataSource
              .updateTransaction(updatedLocalTransaction);
          log('Resolved transaction conflict: using local version (${_conflictResolutionStrategy.name})',
              name: 'SyncService');
          return const BidirectionalSyncResult(remoteUpdated: true);

        case ConflictResolution.useRemote:
          await _transactionLocalDataSource
              .editTransaction(remoteTransaction.toHiveModel());
          log('Resolved transaction conflict: using remote version (${_conflictResolutionStrategy.name})',
              name: 'SyncService');
          return const BidirectionalSyncResult(localUpdated: true);

        case ConflictResolution.requiresManualResolution:
          _pendingConflicts.add(conflictInfo);
          _conflictController.add(List.from(_pendingConflicts));
          log('Transaction conflict requires manual resolution: ${remoteTransaction.id}',
              name: 'SyncService');
          return const BidirectionalSyncResult();

        case ConflictResolution.noConflict:
          return const BidirectionalSyncResult();
      }
    } catch (e) {
      log('Error resolving transaction conflict: $e', name: 'SyncService');
      return BidirectionalSyncResult(error: e.toString());
    }
  }

  /// Enhanced bidirectional category conflict resolution
  Future<BidirectionalSyncResult> _resolveCategoryConflictBidirectional(
      dynamic localCategory, CategoryFirestoreModel remoteCategory) async {
    try {
      // Convert local category to Firestore model for comparison
      final localFirestoreCategory =
          CategoryFirestoreModel.fromHiveModel(localCategory, _currentUserId!);

      // Create conflict info for strategy-based resolution
      final conflictInfo = ConflictInfo(
        id: remoteCategory.id,
        dataType: 'category',
        localData: localFirestoreCategory.toFirestore(),
        remoteData: remoteCategory.toFirestore(),
        localUpdatedAt: localFirestoreCategory.updatedAt,
        remoteUpdatedAt: remoteCategory.updatedAt,
        localVersion: localFirestoreCategory.version,
        remoteVersion: remoteCategory.version,
      );

      // Apply conflict resolution strategy
      final resolution = _resolveConflictWithStrategy(conflictInfo);

      switch (resolution) {
        case ConflictResolution.useLocal:
          final updatedLocalCategory = localFirestoreCategory.copyWith(
            version: localFirestoreCategory.version + 1,
            updatedAt: DateTime.now(),
          );
          await _categoryRemoteDataSource.updateCategory(updatedLocalCategory);
          log('Resolved category conflict: using local version (${_conflictResolutionStrategy.name})',
              name: 'SyncService');
          return const BidirectionalSyncResult(remoteUpdated: true);

        case ConflictResolution.useRemote:
          await _categoryLocalDataSource
              .addCategory(remoteCategory.toHiveModel());
          log('Resolved category conflict: using remote version (${_conflictResolutionStrategy.name})',
              name: 'SyncService');
          return const BidirectionalSyncResult(localUpdated: true);

        case ConflictResolution.requiresManualResolution:
          _pendingConflicts.add(conflictInfo);
          _conflictController.add(List.from(_pendingConflicts));
          log('Category conflict requires manual resolution: ${remoteCategory.id}',
              name: 'SyncService');
          return const BidirectionalSyncResult();

        case ConflictResolution.noConflict:
          return const BidirectionalSyncResult();
      }
    } catch (e) {
      log('Error resolving category conflict: $e', name: 'SyncService');
      return BidirectionalSyncResult(error: e.toString());
    }
  }

  /// Legacy transaction conflict resolution (kept for backward compatibility)
  Future<void> _resolveTransactionConflict(dynamic localTransaction,
      TransactionFirestoreModel remoteTransaction) async {
    final result = await _resolveTransactionConflictBidirectional(
        localTransaction, remoteTransaction);
    if (result.error != null) {
      log('Legacy transaction conflict resolution failed: ${result.error}',
          name: 'SyncService');
    }
  }

  /// Legacy category conflict resolution (kept for backward compatibility)
  Future<void> _resolveCategoryConflict(
      dynamic localCategory, CategoryFirestoreModel remoteCategory) async {
    final result = await _resolveCategoryConflictBidirectional(
        localCategory, remoteCategory);
    if (result.error != null) {
      log('Legacy category conflict resolution failed: ${result.error}',
          name: 'SyncService');
    }
  }

  /// Clear all local data (called on logout)
  Future<void> clearLocalData() async {
    try {
      log('Clearing all local data', name: 'SyncService');

      // Stop all listeners and timers
      await _stopRealTimeListeners();
      await _connectivitySubscription?.cancel();
      _periodicSyncTimer?.cancel();

      // Clear all local data
      final categoryBox = await _hive.openBox('category-database');
      final transactionBox = await _hive.openBox(DBConstants.transactionDbName);
      final syncBox =
          await _hive.openBox<SyncOperationModel>('sync-operations');

      await categoryBox.clear();
      await transactionBox.clear();
      await syncBox.clear();

      _currentUserId = null;
      _updateSyncStatus(SyncStatus.idle);

      // Reset sync tracking counters
      _lastSyncTime = null;
      _totalSyncedTransactions = 0;
      _totalSyncedCategories = 0;
      _totalFailedOperations = 0;
      _resetSyncSessionStats();

      // Reset offline/online state
      _wasOffline = false;
      _lastOnlineTime = null;
      _lastOfflineTime = null;
      _consecutiveFailures = 0;
      _retryTimer?.cancel();
      _retryTimer = null;

      // Clear conflicts
      _pendingConflicts.clear();

      log('Local data cleared successfully', name: 'SyncService');
    } catch (e) {
      log('Error clearing local data: $e', name: 'SyncService');
      throw DatabaseFailure(
          message: 'Failed to clear local data: ${e.toString()}');
    }
  }

  /// Queue a sync operation for later execution
  Future<void> queueSyncOperation(SyncOperationModel operation) async {
    try {
      final syncBox =
          await _hive.openBox<SyncOperationModel>('sync-operations');
      await syncBox.put(operation.id, operation);

      log('Queued sync operation: ${operation.operationType} ${operation.dataType} ${operation.dataId}',
          name: 'SyncService');

      // If online, try to sync immediately
      if (_connectivityService.isConnected) {
        await _syncPendingOperations();
      }
    } catch (e) {
      log('Error queuing sync operation: $e', name: 'SyncService');
      throw DatabaseFailure(
          message: 'Failed to queue sync operation: ${e.toString()}');
    }
  }

  /// Force sync now (manual sync trigger)
  Future<SyncResult> forceSyncNow() async {
    if (_currentUserId == null) {
      return const SyncResult(success: false, error: 'No user logged in');
    }

    if (!_connectivityService.isConnected) {
      return const SyncResult(success: false, error: 'No internet connection');
    }

    log('Force sync triggered', name: 'SyncService');
    _updateSyncStatus(SyncStatus.syncing);

    final syncStartTime = DateTime.now();
    _resetSyncSessionStats();

    try {
      await _syncPendingOperations();
      await _syncFromRemoteToLocal();

      final syncEndTime = DateTime.now();
      final syncDuration = syncEndTime.difference(syncStartTime);
      _lastSyncTime = syncEndTime;

      final result = SyncResult(
        success: true,
        syncedTransactions: _syncSessionStats['transactions']!,
        syncedCategories: _syncSessionStats['categories']!,
        failedOperations: _syncSessionStats['failed']!,
        pendingOperations: await _getPendingOperationsCount(),
        lastSyncTime: _lastSyncTime,
        syncDuration: syncDuration,
      );

      // Update total counters
      _totalSyncedTransactions += _syncSessionStats['transactions']!;
      _totalSyncedCategories += _syncSessionStats['categories']!;
      _totalFailedOperations += _syncSessionStats['failed']!;

      _updateSyncStatus(SyncStatus.success);
      _syncResultController.add(result);

      log('Force sync completed: ${_syncSessionStats['transactions']} transactions, ${_syncSessionStats['categories']} categories in ${syncDuration.inMilliseconds}ms',
          name: 'SyncService');

      return result;
    } catch (e) {
      log('Force sync failed: $e', name: 'SyncService');
      _updateSyncStatus(SyncStatus.error);

      final syncEndTime = DateTime.now();
      final syncDuration = syncEndTime.difference(syncStartTime);

      final result = SyncResult(
        success: false,
        error: e.toString(),
        syncedTransactions: _syncSessionStats['transactions']!,
        syncedCategories: _syncSessionStats['categories']!,
        failedOperations: _syncSessionStats['failed']! + 1,
        pendingOperations: await _getPendingOperationsCount(),
        lastSyncTime: _lastSyncTime,
        syncDuration: syncDuration,
      );

      _syncResultController.add(result);
      return result;
    }
  }

  /// Get sync statistics
  Future<Map<String, dynamic>> getSyncStats() async {
    try {
      final syncBox =
          await _hive.openBox<SyncOperationModel>('sync-operations');
      final pendingOps =
          syncBox.values.where((op) => op.userId == _currentUserId).toList();

      return {
        'isOnline': _connectivityService.isConnected,
        'currentStatus': _currentStatus.name,
        'pendingOperations': pendingOps.length,
        'pendingTransactions': pendingOps
            .where((op) => op.dataType == SyncDataType.transaction)
            .length,
        'pendingCategories': pendingOps
            .where((op) => op.dataType == SyncDataType.category)
            .length,
        'lastSyncTime': _lastSyncTime?.toIso8601String(),
        'totalSyncedTransactions': _totalSyncedTransactions,
        'totalSyncedCategories': _totalSyncedCategories,
        'totalFailedOperations': _totalFailedOperations,
        'currentSessionTransactions': _syncSessionStats['transactions'],
        'currentSessionCategories': _syncSessionStats['categories'],
        'currentSessionFailed': _syncSessionStats['failed'],
      };
    } catch (e) {
      log('Error getting sync stats: $e', name: 'SyncService');
      return {
        'isOnline': _connectivityService.isConnected,
        'currentStatus': _currentStatus.name,
        'pendingOperations': 0,
        'totalSyncedTransactions': _totalSyncedTransactions,
        'totalSyncedCategories': _totalSyncedCategories,
        'totalFailedOperations': _totalFailedOperations,
        'error': e.toString(),
      };
    }
  }

  /// Get error statistics
  Map<String, dynamic> get errorStats {
    final errorsByType = <String, int>{};
    for (final error in _recentErrors) {
      errorsByType[error.type.name] = (errorsByType[error.type.name] ?? 0) + 1;
    }

    return {
      'totalErrors': _recentErrors.length,
      'errorsByType': errorsByType,
      'lastError': _recentErrors.isNotEmpty
          ? {
              'type': _recentErrors.last.type.name,
              'message': _recentErrors.last.message,
              'timestamp': _recentErrors.last.timestamp.toIso8601String(),
              'isRetryable': _recentErrors.last.isRetryable,
            }
          : null,
      'consecutiveFailures': _consecutiveFailures,
    };
  }

  /// Force retry failed operations (manual recovery)
  Future<void> forceRetryFailedOperations() async {
    if (!_connectivityService.isConnected) {
      throw Exception('Cannot retry operations while offline');
    }

    log('Force retrying failed operations', name: 'SyncService');

    try {
      // Reset consecutive failures for fresh start
      _consecutiveFailures = 0;

      // Attempt to sync pending operations
      await _syncPendingOperations();

      log('Force retry completed successfully', name: 'SyncService');
    } catch (e) {
      final syncError = _categorizeError(e);
      _handleSyncError(syncError);
      rethrow;
    }
  }

  /// Dispose of the sync service
  void dispose() {
    _stopRealTimeListeners();
    _connectivitySubscription?.cancel();
    _periodicSyncTimer?.cancel();
    _retryTimer?.cancel();
    _syncStatusController.close();
    _syncResultController.close();
    _conflictController.close();
    _connectivityStatusController.close();
    _progressController.close();
    _errorController.close();
    _pendingConflicts.clear();
    _recentErrors.clear();
    _clearProgress();

    // Clean up object pools to free memory
    _mapPool.clear();
    _listPool.clear();
  }
}
